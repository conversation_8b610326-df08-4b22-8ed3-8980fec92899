import axios, { AxiosError } from "axios";

// Create an axios instance for our API
export const apiClient = axios.create({
	baseURL: import.meta.env.VITE_API_URL,
	withCredentials: true,
});

apiClient.interceptors.response.use(
	response => response,
	error => {
		if (error instanceof AxiosError && error.response?.status === 401) {
			window.location.assign("/login");
			localStorage.setItem("loggedOut", "true");
			localStorage.setItem("timeStamp", Date.now().toString());
		}
		return Promise.reject(error);
	}
);

// Default axios instance for other requests
export const createClient = (baseURL: string) => {
	return axios.create({
		baseURL,
		withCredentials: true,
	});
};

export default apiClient;
