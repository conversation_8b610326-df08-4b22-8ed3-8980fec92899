import {
	createContext,
	useState,
	useContext,
	useEffect,
	useCallback,
	useRef,
} from "react";
import type { ReactNode } from "react";
import { apiClient } from "../config/axios";
import type { AuthContextType, Session } from "../types";

const AuthContext = createContext<AuthContextType | null>(null);

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
	const context = useContext(AuthContext);
	if (!context) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};

interface AuthProviderProps {
	children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
	const didRun = useRef(false);
	const [user, setUser] = useState<Session | null>(null);
	const [loading, setLoading] = useState(true);
	const [isUpdatingUser, setIsUpdatingUser] = useState(false);
	const [isSwitchingAdminPanel, setIsSwitchingAdminPanel] = useState(false);

	const fetchUser = useCallback(
		async (isUpdate = false): Promise<Session | null> => {
			try {
				if (isUpdate) {
					setIsUpdatingUser(true);
				}
				const response =
					await apiClient.get<Session>("/api/auth/session");
				setUser(response.data);
				return response.data;
			} catch (error) {
				console.log(error);
				setUser(null);
				return null;
			} finally {
				setLoading(false);
				if (isUpdate) {
					setIsUpdatingUser(false);
				}
			}
		},
		[]
	);

	useEffect(() => {
		if (!didRun.current) {
			didRun.current = true;
			fetchUser();
		}
	}, [fetchUser]);

	const login = async (email: string, password: string): Promise<Session> => {
		await apiClient.post("/api/auth/login", { email, password });
		// Cookie is set automatically by the server, no need to handle token manually
		const fetchedUser = await fetchUser();
		return fetchedUser as Session;
	};

	const logout = async () => {
		try {
			await apiClient.post("/api/auth/logout");
		} catch (error) {
			console.log("Logout error:", error);
		} finally {
			setUser(null);
		}
	};

	const switchAdminPanel = async (): Promise<{ message: string }> => {
		try {
			setIsSwitchingAdminPanel(true);
			const response = await apiClient.post(
				"/api/users/changeAdminPanelView"
			);
			await fetchUser(true);
			return response.data;
		} finally {
			setIsSwitchingAdminPanel(false);
		}
	};
	const value: AuthContextType = {
		user,
		loading,
		isUpdatingUser,
		isSwitchingAdminPanel,
		login,
		logout,
		setUser,
		fetchUser,
		switchAdminPanel,
	};

	return (
		<AuthContext.Provider value={value}>
			{!loading && children}
		</AuthContext.Provider>
	);
}
